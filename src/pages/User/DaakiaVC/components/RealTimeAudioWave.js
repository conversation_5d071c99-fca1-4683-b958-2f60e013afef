import React, { useEffect, useRef, useState } from 'react';
import { useLocalParticipant } from '@livekit/components-react';
import '../styles/RealTimeAudioWave.scss';

function RealTimeAudioWave({ className = '' }) {
  const { localParticipant } = useLocalParticipant();
  const [audioLevel, setAudioLevel] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [showDots, setShowDots] = useState(false);
  const analyserRef = useRef();
  const animationRef = useRef();
  const prevMicState = useRef(false);

  // Handle microphone state changes
  useEffect(() => {
    const currentMicState = localParticipant?.isMicrophoneEnabled || false;

    if (currentMicState !== prevMicState.current) {
      if (currentMicState) {
        // Mic turned ON - show circle-to-dots animation
        setIsTransitioning(true);
        setShowDots(false);

        const timer = setTimeout(() => {
          setShowDots(true);
          setIsTransitioning(false);
        }, 1500); // Match animation duration

        return () => clearTimeout(timer);
      } else {
        // Mic turned OFF - show dots-to-circle animation then hide
        setIsTransitioning(true);
        setShowDots(false);

        const timer = setTimeout(() => {
          setIsTransitioning(false);
        }, 1500);

        return () => clearTimeout(timer);
      }
    }

    prevMicState.current = currentMicState;
  }, [localParticipant?.isMicrophoneEnabled]);

  useEffect(() => {
    let audioContext;
    let sourceNode;

    const updateAudioLevel = () => {
      if (!analyserRef.current) return;

      const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
      analyserRef.current.getByteFrequencyData(dataArray);

      const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
      const normalizedLevel = Math.min(average / 128, 1);

      setAudioLevel(normalizedLevel);
      animationRef.current = requestAnimationFrame(updateAudioLevel);
    };

    const setupAudioAnalysis = async () => {
      try {
        if (!localParticipant?.isMicrophoneEnabled) {
          setAudioLevel(0);
          return;
        }

        const micTrack = localParticipant.getTrackPublication('microphone')?.track;
        if (!micTrack?.mediaStream) return;

        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        analyserRef.current = audioContext.createAnalyser();
        analyserRef.current.fftSize = 256;
        analyserRef.current.smoothingTimeConstant = 0.8;

        sourceNode = audioContext.createMediaStreamSource(micTrack.mediaStream);
        sourceNode.connect(analyserRef.current);

        updateAudioLevel();
      } catch (error) {
        console.error('Error setting up audio analysis:', error);
        setAudioLevel(0);
      }
    };

    setupAudioAnalysis();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (audioContext && audioContext.state !== 'closed') {
        audioContext.close();
      }
      if (sourceNode) {
        sourceNode.disconnect();
      }
    };
  }, [localParticipant?.isMicrophoneEnabled]);

  const isActive = localParticipant?.isMicrophoneEnabled;
  const isSpeaking = audioLevel > 0.1;

  // Create different scaling values for each dot to enhance wave effect
  const dot1Scale = 1 + (audioLevel * 0.3);
  const dot2Scale = 1 + (audioLevel * 0.5); // Middle dot gets more emphasis
  const dot3Scale = 1 + (audioLevel * 0.3);

  // Don't render anything when mic is off and not transitioning
  if (!isActive && !isTransitioning) {
    return null;
  }

  return (
    <div
      className={`audio-dots ${className} ${isActive ? 'active' : 'inactive'} ${isSpeaking ? 'speaking' : ''} ${isTransitioning ? 'transitioning' : ''} ${showDots ? 'show-dots' : 'show-circle'}`}
      style={{ '--audio-level': audioLevel }}
    >
      {/* Circle state - shown when transitioning or mic just turned on */}
      {!showDots && (
        <div className="circle" />
      )}

      {/* Dots state - shown after transition completes */}
      {showDots && (
        <>
          <div
            className="dot"
            style={{ '--dot-scale': dot1Scale }}
          />
          <div
            className="dot"
            style={{ '--dot-scale': dot2Scale }}
          />
          <div
            className="dot"
            style={{ '--dot-scale': dot3Scale }}
          />
        </>
      )}
    </div>
  );
}

export default RealTimeAudioWave;
