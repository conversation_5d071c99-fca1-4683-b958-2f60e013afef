.audio-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 0.5rem;
  height: 100%;
  width: 40px;
  position: relative;
  overflow: hidden;

  .static-circle {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4285f4 0%, #34a853 25%, #fbbc04 50%, #ea4335 75%, #9c27b0 100%);
    background-size: 200% 200%;
    animation: gradient-shift 3s ease-in-out infinite;
    opacity: 0.4;
    box-shadow:
      0 0 8px rgba(66, 133, 244, 0.4),
      0 0 16px rgba(52, 168, 83, 0.3);
  }

  // Circle that explodes into particles
  .initial-circle {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4285f4 0%, #34a853 25%, #fbbc04 50%, #ea4335 75%, #9c27b0 100%);
    background-size: 200% 200%;
    animation: explosion-source 1.8s ease-out forwards, gradient-shift 3s ease-in-out infinite;
    box-shadow:
      0 0 20px rgba(66, 133, 244, 0.6),
      0 0 40px rgba(52, 168, 83, 0.4);
    z-index: 3;
  }

  // Explosion particles
  &.transitioning {
    // Create multiple particles that explode outward
    &::before,
    &::after {
      content: '';
      position: absolute;
      left: 50%;
      top: 50%;
      width: 3px;
      height: 3px;
      border-radius: 50%;
      background: linear-gradient(135deg, #4285f4 0%, #34a853 25%, #fbbc04 50%, #ea4335 75%, #9c27b0 100%);
      opacity: 0;
      z-index: 2;
    }

    &::before {
      animation: particle-explosion-1 1.8s ease-out 0.3s forwards;
    }

    &::after {
      animation: particle-explosion-2 1.8s ease-out 0.4s forwards;
    }

    // Main dots that settle into position
    .dot {
      opacity: 0;
      transform: scale(0);

      &:nth-child(1) {
        animation: particle-settle-1 1.5s ease-out 0.6s forwards;
      }
      &:nth-child(2) {
        animation: particle-settle-2 1.5s ease-out 0.8s forwards;
      }
      &:nth-child(3) {
        animation: particle-settle-3 1.5s ease-out 1.0s forwards;
      }
    }
  }

  // Additional sparkle particles (will be added via pseudo-elements on dots)
  &.transitioning .dot {
    &::before {
      content: '';
      position: absolute;
      width: 2px;
      height: 2px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.8);
      opacity: 0;
      animation: sparkle-particle 1.2s ease-out forwards;
    }

    &:nth-child(1)::before {
      animation-delay: 0.5s;
      left: -10px;
      top: -8px;
    }
    &:nth-child(2)::before {
      animation-delay: 0.7s;
      left: 8px;
      top: -12px;
    }
    &:nth-child(3)::before {
      animation-delay: 0.9s;
      left: -6px;
      top: 10px;
    }
  }

  .dot {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    opacity: 0.3;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
    position: relative;

    // Gemini AI inspired gradient colors with glow
    background: linear-gradient(135deg, #4285f4 0%, #34a853 25%, #fbbc04 50%, #ea4335 75%, #9c27b0 100%);
    background-size: 200% 200%;
    animation: gradient-shift 3s ease-in-out infinite;
    box-shadow:
      0 0 8px rgba(66, 133, 244, 0.4),
      0 0 16px rgba(52, 168, 83, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);

    // Enhanced glow layer for hard glowing effect
    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(135deg, #4285f4 0%, #34a853 25%, #fbbc04 50%, #ea4335 75%, #9c27b0 100%);
      background-size: 200% 200%;
      border-radius: 50%;
      z-index: -1;
      opacity: 0;
      filter: blur(4px);
      transition: opacity 0.4s ease;
      animation: gradient-shift 3s ease-in-out infinite reverse;
    }

    // Different animation delays for each dot
    &:nth-child(1) { animation-delay: 0s; }
    &:nth-child(2) { animation-delay: 1s; }
    &:nth-child(3) { animation-delay: 2s; }
  }

  &.active .dot {
    opacity: 0.8;
    transform: scale(1.2);

    &::before {
      opacity: 0.4;
    }
  }

  &.speaking .dot {
    opacity: 1;
    animation: gemini-wave-motion 0.8s ease-in-out infinite, gradient-shift 2s ease-in-out infinite;

    &::before {
      opacity: 0.7;
    }

    // Enhanced Gemini-style glow effects for speaking
    &:nth-child(1) {
      animation-delay: 0s, 0s;
      box-shadow:
        0 0 16px rgba(66, 133, 244, 0.8),
        0 0 32px rgba(66, 133, 244, 0.4),
        0 0 48px rgba(66, 133, 244, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }
    &:nth-child(2) {
      animation-delay: 0.27s, 1s;
      box-shadow:
        0 0 16px rgba(52, 168, 83, 0.8),
        0 0 32px rgba(251, 188, 4, 0.4),
        0 0 48px rgba(234, 67, 53, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }
    &:nth-child(3) {
      animation-delay: 0.54s, 2s;
      box-shadow:
        0 0 16px rgba(156, 39, 176, 0.8),
        0 0 32px rgba(234, 67, 53, 0.4),
        0 0 48px rgba(251, 188, 4, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }
  }
}



// Explosion source circle animation
@keyframes explosion-source {
  0% {
    width: 16px;
    height: 16px;
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
    filter: brightness(1);
  }
  20% {
    transform: translate(-50%, -50%) scale(1.2);
    filter: brightness(1.3);
  }
  30% {
    transform: translate(-50%, -50%) scale(1.4);
    filter: brightness(1.5);
    box-shadow:
      0 0 30px rgba(66, 133, 244, 0.8),
      0 0 60px rgba(52, 168, 83, 0.6);
  }
  40% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.8;
    filter: brightness(2);
  }
  100% {
    width: 4px;
    height: 4px;
    opacity: 0;
    transform: translate(-50%, -50%) scale(0);
    filter: brightness(0);
  }
}

// Particle explosion 1 - swirls and dances
@keyframes particle-explosion-1 {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
  }
  20% {
    opacity: 0.8;
    transform: translate(-30px, -20px) scale(1.2) rotate(180deg);
  }
  40% {
    opacity: 0.6;
    transform: translate(-50px, 10px) scale(0.8) rotate(360deg);
  }
  60% {
    opacity: 0.4;
    transform: translate(-20px, -30px) scale(1.1) rotate(540deg);
  }
  80% {
    opacity: 0.2;
    transform: translate(-60px, 20px) scale(0.6) rotate(720deg);
  }
  100% {
    opacity: 0;
    transform: translate(-80px, -40px) scale(0) rotate(900deg);
  }
}

// Particle explosion 2 - different trajectory
@keyframes particle-explosion-2 {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
  }
  20% {
    opacity: 0.8;
    transform: translate(25px, -25px) scale(1.3) rotate(-180deg);
  }
  40% {
    opacity: 0.6;
    transform: translate(45px, 15px) scale(0.7) rotate(-360deg);
  }
  60% {
    opacity: 0.4;
    transform: translate(15px, -35px) scale(1.2) rotate(-540deg);
  }
  80% {
    opacity: 0.2;
    transform: translate(70px, 25px) scale(0.5) rotate(-720deg);
  }
  100% {
    opacity: 0;
    transform: translate(90px, -50px) scale(0) rotate(-900deg);
  }
}

// Gemini AI inspired gradient animation
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// Particle 1 settles into left dot position
@keyframes particle-settle-1 {
  0% {
    opacity: 0;
    transform: translate(-40px, -30px) scale(0) rotate(720deg);
  }
  30% {
    opacity: 0.8;
    transform: translate(-20px, -15px) scale(1.3) rotate(540deg);
  }
  60% {
    opacity: 0.6;
    transform: translate(-10px, -5px) scale(0.8) rotate(360deg);
  }
  80% {
    opacity: 0.4;
    transform: translate(-4px, 2px) scale(1.1) rotate(180deg);
  }
  100% {
    opacity: 0.3;
    transform: translate(0, 0) scale(1) rotate(0deg);
  }
}

// Particle 2 settles into center dot position
@keyframes particle-settle-2 {
  0% {
    opacity: 0;
    transform: translate(0, -50px) scale(0) rotate(-720deg);
  }
  30% {
    opacity: 0.8;
    transform: translate(0, -25px) scale(1.4) rotate(-540deg);
  }
  60% {
    opacity: 0.6;
    transform: translate(0, -10px) scale(0.7) rotate(-360deg);
  }
  80% {
    opacity: 0.4;
    transform: translate(0, -2px) scale(1.2) rotate(-180deg);
  }
  100% {
    opacity: 0.3;
    transform: translate(0, 0) scale(1) rotate(0deg);
  }
}

// Particle 3 settles into right dot position
@keyframes particle-settle-3 {
  0% {
    opacity: 0;
    transform: translate(40px, -30px) scale(0) rotate(720deg);
  }
  30% {
    opacity: 0.8;
    transform: translate(20px, -15px) scale(1.3) rotate(540deg);
  }
  60% {
    opacity: 0.6;
    transform: translate(10px, -5px) scale(0.8) rotate(360deg);
  }
  80% {
    opacity: 0.4;
    transform: translate(4px, 2px) scale(1.1) rotate(180deg);
  }
  100% {
    opacity: 0.3;
    transform: translate(0, 0) scale(1) rotate(0deg);
  }
}

// Sparkle particles that fade away
@keyframes sparkle-particle {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  20% {
    opacity: 1;
    transform: scale(1.5) rotate(180deg);
  }
  40% {
    opacity: 0.8;
    transform: scale(1) rotate(360deg);
  }
  60% {
    opacity: 0.6;
    transform: scale(1.2) rotate(540deg);
  }
  80% {
    opacity: 0.3;
    transform: scale(0.8) rotate(720deg);
  }
  100% {
    opacity: 0;
    transform: scale(0) rotate(900deg);
  }
}

// Enhanced wave motion with Gemini-style fluidity
@keyframes gemini-wave-motion {
  0% {
    transform: translateY(0) scale(var(--dot-scale, 1));
    filter: brightness(1);
  }
  25% {
    transform: translateY(-4px) scale(calc(var(--dot-scale, 1) * 1.2));
    filter: brightness(1.2);
  }
  50% {
    transform: translateY(0) scale(calc(var(--dot-scale, 1) * 1.1));
    filter: brightness(1.1);
  }
  75% {
    transform: translateY(4px) scale(calc(var(--dot-scale, 1) * 1.2));
    filter: brightness(1.2);
  }
  100% {
    transform: translateY(0) scale(var(--dot-scale, 1));
    filter: brightness(1);
  }
}


