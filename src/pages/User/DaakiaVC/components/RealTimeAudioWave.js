import React, { useEffect, useRef, useState } from 'react';
import { useLocalParticipant } from '@livekit/components-react';
import '../styles/RealTimeAudioWave.scss';

function RealTimeAudioWave({ className = '' }) {
  const { localParticipant } = useLocalParticipant();
  const [audioLevel, setAudioLevel] = useState(0);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const analyserRef = useRef();
  const animationRef = useRef();

  // Handle initial load animation
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitialLoad(false);
    }, 1500); // Match the animation duration

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    let audioContext;
    let sourceNode;

    const updateAudioLevel = () => {
      if (!analyserRef.current) return;

      const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
      analyserRef.current.getByteFrequencyData(dataArray);

      const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
      const normalizedLevel = Math.min(average / 128, 1);

      setAudioLevel(normalizedLevel);
      animationRef.current = requestAnimationFrame(updateAudioLevel);
    };

    const setupAudioAnalysis = async () => {
      try {
        if (!localParticipant?.isMicrophoneEnabled) {
          setAudioLevel(0);
          return;
        }

        const micTrack = localParticipant.getTrackPublication('microphone')?.track;
        if (!micTrack?.mediaStream) return;

        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        analyserRef.current = audioContext.createAnalyser();
        analyserRef.current.fftSize = 256;
        analyserRef.current.smoothingTimeConstant = 0.8;

        sourceNode = audioContext.createMediaStreamSource(micTrack.mediaStream);
        sourceNode.connect(analyserRef.current);

        updateAudioLevel();
      } catch (error) {
        console.error('Error setting up audio analysis:', error);
        setAudioLevel(0);
      }
    };

    setupAudioAnalysis();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (audioContext && audioContext.state !== 'closed') {
        audioContext.close();
      }
      if (sourceNode) {
        sourceNode.disconnect();
      }
    };
  }, [localParticipant?.isMicrophoneEnabled]);

  const isActive = localParticipant?.isMicrophoneEnabled;
  const isSpeaking = audioLevel > 0.1;

  // Create different scaling values for each dot to enhance wave effect
  const dot1Scale = 1 + (audioLevel * 0.3);
  const dot2Scale = 1 + (audioLevel * 0.5); // Middle dot gets more emphasis
  const dot3Scale = 1 + (audioLevel * 0.3);

  return (
    <div
      className={`audio-dots ${className} ${isActive ? 'active' : 'inactive'} ${isSpeaking ? 'speaking' : ''} ${isInitialLoad ? 'initial-load' : ''}`}
      style={{ '--audio-level': audioLevel }}
    >
      <div
        className="dot"
        style={{ '--dot-scale': dot1Scale }}
      />
      <div
        className="dot"
        style={{ '--dot-scale': dot2Scale }}
      />
      <div
        className="dot"
        style={{ '--dot-scale': dot3Scale }}
      />
    </div>
  );
}

export default RealTimeAudioWave;
