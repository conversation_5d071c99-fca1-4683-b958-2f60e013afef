.audio-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 0.5rem;
  height: 24px;
  position: relative;

  // Initial state - show as single circle
  &.initial-load {
    .dot {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 12px;
      height: 12px;
      opacity: 1;
      animation: circle-to-dots 1.5s ease-out forwards;

      &:nth-child(1) { animation-delay: 0s; }
      &:nth-child(2) { animation-delay: 0.1s; }
      &:nth-child(3) { animation-delay: 0.2s; }
    }
  }

  .dot {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    opacity: 0.3;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform-origin: center;
    position: relative;

    // Gemini AI inspired gradient colors with enhanced glow
    background: linear-gradient(135deg, #4285f4 0%, #34a853 25%, #fbbc04 50%, #ea4335 75%, #9c27b0 100%);
    box-shadow:
      0 0 8px rgba(66, 133, 244, 0.4),
      0 0 16px rgba(52, 168, 83, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);

    // Animated gradient background
    background-size: 200% 200%;
    animation: gradient-shift 3s ease-in-out infinite;

    // Enhanced glow layer
    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(135deg, #4285f4 0%, #34a853 25%, #fbbc04 50%, #ea4335 75%, #9c27b0 100%);
      background-size: 200% 200%;
      border-radius: 50%;
      z-index: -1;
      opacity: 0;
      filter: blur(4px);
      transition: opacity 0.4s ease;
      animation: gradient-shift 3s ease-in-out infinite reverse;
    }

    // Sparkle effect
    &::after {
      content: '';
      position: absolute;
      top: 1px;
      left: 1px;
      width: 1px;
      height: 1px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 50%;
      opacity: 0;
      animation: sparkle 2s ease-in-out infinite;
    }

    // Different animation delays and slight color variations
    &:nth-child(1) {
      animation-delay: 0s;
      filter: hue-rotate(0deg);

      &::after { animation-delay: 0s; }
    }

    &:nth-child(2) {
      animation-delay: 1s;
      filter: hue-rotate(120deg);

      &::after { animation-delay: 0.7s; }
    }

    &:nth-child(3) {
      animation-delay: 2s;
      filter: hue-rotate(240deg);

      &::after { animation-delay: 1.4s; }
    }
  }

  &.active .dot {
    opacity: 0.8;
    transform: scale(1.2);

    &::before {
      opacity: 0.4;
    }

    &::after {
      opacity: 1;
    }
  }

  &.speaking .dot {
    opacity: 1;
    animation: gemini-wave-motion 0.8s ease-in-out infinite, gradient-shift 2s ease-in-out infinite;

    &::before {
      opacity: 0.7;
    }

    &::after {
      opacity: 1;
      animation: sparkle 1s ease-in-out infinite, twinkle 0.8s ease-in-out infinite;
    }

    // Enhanced Gemini-style glow effects
    &:nth-child(1) {
      animation-delay: 0s, 0s;
      box-shadow:
        0 0 16px rgba(66, 133, 244, 0.8),
        0 0 32px rgba(66, 133, 244, 0.4),
        0 0 48px rgba(66, 133, 244, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }
    &:nth-child(2) {
      animation-delay: 0.27s, 1s;
      box-shadow:
        0 0 16px rgba(52, 168, 83, 0.8),
        0 0 32px rgba(251, 188, 4, 0.4),
        0 0 48px rgba(234, 67, 53, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }
    &:nth-child(3) {
      animation-delay: 0.54s, 2s;
      box-shadow:
        0 0 16px rgba(156, 39, 176, 0.8),
        0 0 32px rgba(234, 67, 53, 0.4),
        0 0 48px rgba(251, 188, 4, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }
  }
}

// Initial circle to dots transformation animation
@keyframes circle-to-dots {
  0% {
    width: 12px;
    height: 12px;
    opacity: 1;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
    background: linear-gradient(135deg, #4285f4 0%, #34a853 25%, #fbbc04 50%, #ea4335 75%, #9c27b0 100%);
    box-shadow: 0 0 20px rgba(66, 133, 244, 0.6);
  }
  30% {
    transform: translate(-50%, -50%) scale(1.3) rotate(180deg);
    box-shadow: 0 0 30px rgba(52, 168, 83, 0.8);
  }
  60% {
    width: 8px;
    height: 8px;
    transform: translate(-50%, -50%) scale(0.8) rotate(360deg);
    box-shadow: 0 0 15px rgba(251, 188, 4, 0.6);
  }
  80% {
    width: 4px;
    height: 4px;
    opacity: 0.8;
  }
  100% {
    width: 4px;
    height: 4px;
    opacity: 0.3;
    transform: translate(0, 0) scale(1) rotate(0deg);
    position: relative;
    left: auto;
    top: auto;
  }
}

// Gemini AI inspired gradient animation
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
    filter: hue-rotate(0deg) saturate(1);
  }
  25% {
    background-position: 50% 0%;
    filter: hue-rotate(90deg) saturate(1.2);
  }
  50% {
    background-position: 100% 50%;
    filter: hue-rotate(180deg) saturate(1.1);
  }
  75% {
    background-position: 50% 100%;
    filter: hue-rotate(270deg) saturate(1.2);
  }
  100% {
    background-position: 0% 50%;
    filter: hue-rotate(360deg) saturate(1);
  }
}

// Enhanced wave motion with Gemini-style fluidity
@keyframes gemini-wave-motion {
  0% {
    transform: translateY(0) scale(var(--dot-scale, 1)) rotate(0deg);
    filter: brightness(1) saturate(1);
  }
  25% {
    transform: translateY(-5px) scale(calc(var(--dot-scale, 1) * 1.3)) rotate(90deg);
    filter: brightness(1.3) saturate(1.2);
  }
  50% {
    transform: translateY(0) scale(calc(var(--dot-scale, 1) * 1.1)) rotate(180deg);
    filter: brightness(1.1) saturate(1.1);
  }
  75% {
    transform: translateY(5px) scale(calc(var(--dot-scale, 1) * 1.3)) rotate(270deg);
    filter: brightness(1.3) saturate(1.2);
  }
  100% {
    transform: translateY(0) scale(var(--dot-scale, 1)) rotate(360deg);
    filter: brightness(1) saturate(1);
  }
}

// Sparkle effect for magical touch
@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
}

// Additional twinkle effect
@keyframes twinkle {
  0%, 100% {
    box-shadow: inset 0 0 0 rgba(255, 255, 255, 0);
  }
  50% {
    box-shadow: inset 0 0 4px rgba(255, 255, 255, 0.8);
  }
}
