.audio-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 0.5rem;
  height: 100%;
  width: 40px;
  position: relative;
  overflow: hidden;

  .static-circle {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4285f4 0%, #34a853 25%, #fbbc04 50%, #ea4335 75%, #9c27b0 100%);
    background-size: 200% 200%;
    animation: gradient-shift 3s ease-in-out infinite;
    opacity: 0.4;
    box-shadow:
      0 0 8px rgba(66, 133, 244, 0.4),
      0 0 16px rgba(52, 168, 83, 0.3);
  }

  // Initial circle that creates ripple waves
  .initial-circle {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4285f4 0%, #34a853 25%, #fbbc04 50%, #ea4335 75%, #9c27b0 100%);
    background-size: 200% 200%;
    animation: ripple-source 2s ease-out forwards, gradient-shift 3s ease-in-out infinite;
    box-shadow:
      0 0 20px rgba(66, 133, 244, 0.6),
      0 0 40px rgba(52, 168, 83, 0.4);
    z-index: 3;

    // Create ripple waves
    &::before,
    &::after {
      content: '';
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 16px;
      height: 16px;
      border-radius: 50%;
      border: 2px solid rgba(66, 133, 244, 0.6);
      opacity: 0;
      z-index: -1;
    }

    &::before {
      animation: ripple-wave-1 2s ease-out forwards;
    }

    &::after {
      animation: ripple-wave-2 2s ease-out 0.3s forwards;
    }
  }

  // Dots riding the wave into position
  &.transitioning .dot {
    opacity: 0;
    transform: scale(0);

    &:nth-child(1) {
      animation: dot-wave-ride-1 1.5s ease-out 0.5s forwards;
    }
    &:nth-child(2) {
      animation: dot-wave-ride-2 1.5s ease-out 0.8s forwards;
    }
    &:nth-child(3) {
      animation: dot-wave-ride-3 1.5s ease-out 1.1s forwards;
    }
  }

  .dot {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    opacity: 0.3;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
    position: relative;

    // Gemini AI inspired gradient colors with glow
    background: linear-gradient(135deg, #4285f4 0%, #34a853 25%, #fbbc04 50%, #ea4335 75%, #9c27b0 100%);
    background-size: 200% 200%;
    animation: gradient-shift 3s ease-in-out infinite;
    box-shadow:
      0 0 8px rgba(66, 133, 244, 0.4),
      0 0 16px rgba(52, 168, 83, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);

    // Enhanced glow layer for hard glowing effect
    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(135deg, #4285f4 0%, #34a853 25%, #fbbc04 50%, #ea4335 75%, #9c27b0 100%);
      background-size: 200% 200%;
      border-radius: 50%;
      z-index: -1;
      opacity: 0;
      filter: blur(4px);
      transition: opacity 0.4s ease;
      animation: gradient-shift 3s ease-in-out infinite reverse;
    }

    // Different animation delays for each dot
    &:nth-child(1) { animation-delay: 0s; }
    &:nth-child(2) { animation-delay: 1s; }
    &:nth-child(3) { animation-delay: 2s; }
  }

  &.active .dot {
    opacity: 0.8;
    transform: scale(1.2);

    &::before {
      opacity: 0.4;
    }
  }

  &.speaking .dot {
    opacity: 1;
    animation: gemini-wave-motion 0.8s ease-in-out infinite, gradient-shift 2s ease-in-out infinite;

    &::before {
      opacity: 0.7;
    }

    // Enhanced Gemini-style glow effects for speaking
    &:nth-child(1) {
      animation-delay: 0s, 0s;
      box-shadow:
        0 0 16px rgba(66, 133, 244, 0.8),
        0 0 32px rgba(66, 133, 244, 0.4),
        0 0 48px rgba(66, 133, 244, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }
    &:nth-child(2) {
      animation-delay: 0.27s, 1s;
      box-shadow:
        0 0 16px rgba(52, 168, 83, 0.8),
        0 0 32px rgba(251, 188, 4, 0.4),
        0 0 48px rgba(234, 67, 53, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }
    &:nth-child(3) {
      animation-delay: 0.54s, 2s;
      box-shadow:
        0 0 16px rgba(156, 39, 176, 0.8),
        0 0 32px rgba(234, 67, 53, 0.4),
        0 0 48px rgba(251, 188, 4, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }
  }
}



// Ripple source circle animation
@keyframes ripple-source {
  0% {
    width: 16px;
    height: 16px;
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  30% {
    transform: translate(-50%, -50%) scale(1.1);
  }
  70% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.3);
  }
}

// First ripple wave
@keyframes ripple-wave-1 {
  0% {
    width: 16px;
    height: 16px;
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    width: 60px;
    height: 60px;
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    width: 100px;
    height: 100px;
    opacity: 0;
    transform: translate(-50%, -50%) scale(1);
  }
}

// Second ripple wave (delayed)
@keyframes ripple-wave-2 {
  0% {
    width: 16px;
    height: 16px;
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    width: 80px;
    height: 80px;
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    width: 120px;
    height: 120px;
    opacity: 0;
    transform: translate(-50%, -50%) scale(1);
  }
}

// Gemini AI inspired gradient animation
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// Dot 1 rides the wave to left position
@keyframes dot-wave-ride-1 {
  0% {
    opacity: 0;
    transform: translate(0, 0) scale(0) rotate(0deg);
  }
  30% {
    opacity: 0.8;
    transform: translate(-8px, -2px) scale(1.2) rotate(120deg);
  }
  60% {
    transform: translate(-12px, 1px) scale(0.9) rotate(240deg);
  }
  100% {
    opacity: 0.3;
    transform: translate(-8px, 0) scale(1) rotate(360deg);
  }
}

// Dot 2 rides the wave to center position
@keyframes dot-wave-ride-2 {
  0% {
    opacity: 0;
    transform: translate(0, 0) scale(0) rotate(0deg);
  }
  30% {
    opacity: 0.8;
    transform: translate(0, -4px) scale(1.3) rotate(120deg);
  }
  60% {
    transform: translate(0, 2px) scale(0.8) rotate(240deg);
  }
  100% {
    opacity: 0.3;
    transform: translate(0, 0) scale(1) rotate(360deg);
  }
}

// Dot 3 rides the wave to right position
@keyframes dot-wave-ride-3 {
  0% {
    opacity: 0;
    transform: translate(0, 0) scale(0) rotate(0deg);
  }
  30% {
    opacity: 0.8;
    transform: translate(8px, -2px) scale(1.2) rotate(120deg);
  }
  60% {
    transform: translate(12px, 1px) scale(0.9) rotate(240deg);
  }
  100% {
    opacity: 0.3;
    transform: translate(8px, 0) scale(1) rotate(360deg);
  }
}

// Enhanced wave motion with Gemini-style fluidity
@keyframes gemini-wave-motion {
  0% {
    transform: translateY(0) scale(var(--dot-scale, 1));
    filter: brightness(1);
  }
  25% {
    transform: translateY(-4px) scale(calc(var(--dot-scale, 1) * 1.2));
    filter: brightness(1.2);
  }
  50% {
    transform: translateY(0) scale(calc(var(--dot-scale, 1) * 1.1));
    filter: brightness(1.1);
  }
  75% {
    transform: translateY(4px) scale(calc(var(--dot-scale, 1) * 1.2));
    filter: brightness(1.2);
  }
  100% {
    transform: translateY(0) scale(var(--dot-scale, 1));
    filter: brightness(1);
  }
}


