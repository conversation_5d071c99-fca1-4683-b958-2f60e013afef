.audio-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 0.5rem;
  height: 24px; // Fixed height for consistent wave pattern

  .dot {
    width: 6px;
    height: 6px;
    background: #0a84ff;
    border-radius: 50%;
    opacity: 0.3;
    transition: opacity 0.2s ease;
    transform-origin: center;

    // Different animation delays for zigzag effect
    &:nth-child(1) { animation-delay: 0s; }
    &:nth-child(2) { animation-delay: 0.15s; }
    &:nth-child(3) { animation-delay: 0.3s; }
  }

  &.active .dot {
    opacity: 0.6;
  }

  &.speaking .dot {
    opacity: 1;
    animation: wave-motion 0.6s ease-in-out infinite;

    // Different animation delays create the zigzag wave effect
    &:nth-child(1) {
      animation-delay: 0s;
    }
    &:nth-child(2) {
      animation-delay: 0.2s;
    }
    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }
}

// Wave motion for when speaking - creates zigzag pattern
@keyframes wave-motion {
  0%, 100% {
    transform: translateY(0) scale(var(--dot-scale, 1));
  }
  25% {
    transform: translateY(-3px) scale(var(--dot-scale, 1));
  }
  50% {
    transform: translateY(0) scale(var(--dot-scale, 1));
  }
  75% {
    transform: translateY(3px) scale(var(--dot-scale, 1));
  }
}
