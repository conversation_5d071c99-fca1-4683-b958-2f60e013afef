.audio-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 0.5rem;
  height: 24px !important; // Fixed height - NEVER changes
  width: 100% !important; // Fixed width - NEVER changes
  min-height: 24px !important; // Prevent shrinking
  max-height: 24px !important; // Prevent growing
  position: relative;
  overflow: hidden !important; // Prevent any overflow during animations
  box-sizing: border-box !important; // Include padding in size calculations

  // Static circle when mic is off
  .static-circle {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4285f4 0%, #34a853 25%, #fbbc04 50%, #ea4335 75%, #9c27b0 100%);
    background-size: 200% 200%;
    animation: gradient-shift 3s ease-in-out infinite;
    opacity: 0.4;
    box-shadow:
      0 0 8px rgba(66, 133, 244, 0.3),
      0 0 16px rgba(52, 168, 83, 0.2);
  }

  // Initial circle that appears when mic first turns on
  .initial-circle {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4285f4 0%, #34a853 25%, #fbbc04 50%, #ea4335 75%, #9c27b0 100%);
    background-size: 200% 200%;
    animation: circle-to-dots 1.5s ease-out forwards, gradient-shift 3s ease-in-out infinite;
    box-shadow:
      0 0 20px rgba(66, 133, 244, 0.6),
      0 0 40px rgba(52, 168, 83, 0.3);
    z-index: 2;
  }

  .dot {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    opacity: 0.3;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
    position: relative;

    // Gemini AI inspired gradient colors with enhanced glow
    background: linear-gradient(135deg, #4285f4 0%, #34a853 25%, #fbbc04 50%, #ea4335 75%, #9c27b0 100%);
    box-shadow:
      0 0 8px rgba(66, 133, 244, 0.4),
      0 0 16px rgba(52, 168, 83, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);

    // Animated gradient background
    background-size: 200% 200%;
    animation: gradient-shift 3s ease-in-out infinite;

    // Enhanced glow layer
    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(135deg, #4285f4 0%, #34a853 25%, #fbbc04 50%, #ea4335 75%, #9c27b0 100%);
      background-size: 200% 200%;
      border-radius: 50%;
      z-index: -1;
      opacity: 0;
      filter: blur(4px);
      transition: opacity 0.4s ease;
      animation: gradient-shift 3s ease-in-out infinite reverse;
    }

    // Sparkle effect
    &::after {
      content: '';
      position: absolute;
      top: 1px;
      left: 1px;
      width: 1px;
      height: 1px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 50%;
      opacity: 0;
      animation: sparkle 2s ease-in-out infinite;
    }

    // Different animation delays and slight color variations
    &:nth-child(1) {
      animation-delay: 0s;
      filter: hue-rotate(0deg);

      &::after { animation-delay: 0s; }
    }

    &:nth-child(2) {
      animation-delay: 1s;
      filter: hue-rotate(120deg);

      &::after { animation-delay: 0.7s; }
    }

    &:nth-child(3) {
      animation-delay: 2s;
      filter: hue-rotate(240deg);

      &::after { animation-delay: 1.4s; }
    }
  }

  &.active .dot {
    opacity: 0.6;
  }

  &.speaking .dot {
    opacity: 1;
    animation: gemini-wave-motion 0.8s ease-in-out infinite, gradient-shift 2s ease-in-out infinite;

    &::before {
      opacity: 0.7;
    }

    &::after {
      opacity: 1;
    }

    // Different animation delays create the zigzag wave effect
    &:nth-child(1) {
      animation-delay: 0s, 0s;
    }
    &:nth-child(2) {
      animation-delay: 0.27s, 1s;
    }
    &:nth-child(3) {
      animation-delay: 0.54s, 2s;
    }
  }
}



// Circle transforms into dots animation
@keyframes circle-to-dots {
  0% {
    width: 16px;
    height: 16px;
    opacity: 1;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
  }
  30% {
    transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
  }
  60% {
    width: 12px;
    height: 12px;
    transform: translate(-50%, -50%) scale(0.8) rotate(360deg);
  }
  80% {
    width: 8px;
    height: 8px;
    opacity: 0.8;
  }
  100% {
    width: 4px;
    height: 4px;
    opacity: 0;
    transform: translate(-50%, -50%) scale(0) rotate(540deg);
  }
}

// Gemini AI inspired gradient animation
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// Enhanced wave motion with Gemini-style fluidity
@keyframes gemini-wave-motion {
  0% {
    transform: translateY(0) scale(var(--dot-scale, 1));
    filter: brightness(1);
  }
  25% {
    transform: translateY(-4px) scale(calc(var(--dot-scale, 1) * 1.2));
    filter: brightness(1.2);
  }
  50% {
    transform: translateY(0) scale(calc(var(--dot-scale, 1) * 1.1));
    filter: brightness(1.1);
  }
  75% {
    transform: translateY(4px) scale(calc(var(--dot-scale, 1) * 1.2));
    filter: brightness(1.2);
  }
  100% {
    transform: translateY(0) scale(var(--dot-scale, 1));
    filter: brightness(1);
  }
}

// Sparkle effect for magical touch
@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}
