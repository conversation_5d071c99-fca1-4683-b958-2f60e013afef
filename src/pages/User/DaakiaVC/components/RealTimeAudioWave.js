import React, { useEffect, useRef, useState } from 'react';
import { useLocalParticipant } from '@livekit/components-react';
import '../styles/RealTimeAudioWave.scss';

function RealTimeAudioWave({ className = '' }) {
  const { localParticipant } = useLocalParticipant();
  const [audioLevel, setAudioLevel] = useState(0);
  const [showInitialCircle, setShowInitialCircle] = useState(false);
  const [showDots, setShowDots] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [canAnalyzeAudio, setCanAnalyzeAudio] = useState(false);
  const analyserRef = useRef();
  const animationRef = useRef();
  const hasShownInitialAnimation = useRef(false);

  // Handle microphone state changes for initial animation
  useEffect(() => {
    const isActive = localParticipant?.isMicrophoneEnabled;

    if (isActive && !hasShownInitialAnimation.current) {
      // First time mic is turned on - show circle-to-dots animation
      setShowInitialCircle(true);
      setShowDots(false);
      setIsTransitioning(true);
      hasShownInitialAnimation.current = true;

      // Show dots when they start flying away
      const dotsTimer = setTimeout(() => {
        setShowDots(true);
      }, 400); // Start dots when they begin flying away

      // Hide circle after explosion
      const circleTimer = setTimeout(() => {
        setShowInitialCircle(false);
        setIsTransitioning(false);
      }, 3000); // Full animation sequence duration

      // Enable audio analysis ONLY after complete sequence + 500ms buffer
      const audioTimer = setTimeout(() => {
        setCanAnalyzeAudio(true);
      }, 3500); // Wait 500ms after animation for smooth transition

      return () => {
        clearTimeout(dotsTimer);
        clearTimeout(circleTimer);
        clearTimeout(audioTimer);
      };
    } else if (isActive && hasShownInitialAnimation.current) {
      // Mic is on and we've already shown initial animation - just show dots
      setShowInitialCircle(false);
      setShowDots(true);
      setCanAnalyzeAudio(true); // Enable audio analysis immediately for subsequent uses
    } else {
      // Mic is off - disable audio analysis
      setCanAnalyzeAudio(false);
    }
    // When mic is off, we'll show a static circle (handled in render)
  }, [localParticipant?.isMicrophoneEnabled]);

  useEffect(() => {
    let audioContext;
    let sourceNode;

    const updateAudioLevel = () => {
      if (!analyserRef.current || !canAnalyzeAudio) {
        // Don't analyze audio during animation
        setAudioLevel(0);
        animationRef.current = requestAnimationFrame(updateAudioLevel);
        return;
      }

      const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
      analyserRef.current.getByteFrequencyData(dataArray);

      const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
      const normalizedLevel = Math.min(average / 128, 1);

      setAudioLevel(normalizedLevel);
      animationRef.current = requestAnimationFrame(updateAudioLevel);
    };

    const setupAudioAnalysis = async () => {
      try {
        if (!localParticipant?.isMicrophoneEnabled) {
          setAudioLevel(0);
          return;
        }

        const micTrack = localParticipant.getTrackPublication('microphone')?.track;
        if (!micTrack?.mediaStream) return;

        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        analyserRef.current = audioContext.createAnalyser();
        analyserRef.current.fftSize = 256;
        analyserRef.current.smoothingTimeConstant = 0.8;

        sourceNode = audioContext.createMediaStreamSource(micTrack.mediaStream);
        sourceNode.connect(analyserRef.current);

        updateAudioLevel();
      } catch (error) {
        console.error('Error setting up audio analysis:', error);
        setAudioLevel(0);
      }
    };

    setupAudioAnalysis();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (audioContext && audioContext.state !== 'closed') {
        audioContext.close();
      }
      if (sourceNode) {
        sourceNode.disconnect();
      }
    };
  }, [localParticipant?.isMicrophoneEnabled, canAnalyzeAudio]);

  const isActive = localParticipant?.isMicrophoneEnabled;
  const isSpeaking = audioLevel > 0.1;

  // Create different scaling values for each dot to enhance wave effect
  const dot1Scale = 1 + (audioLevel * 0.3);
  const dot2Scale = 1 + (audioLevel * 0.5); // Middle dot gets more emphasis
  const dot3Scale = 1 + (audioLevel * 0.3);

  return (
    <div
      className={`audio-dots ${className} ${isActive ? 'active' : 'inactive'} ${isSpeaking ? 'speaking' : ''} ${isTransitioning ? 'transitioning' : ''}`}
      style={{ '--audio-level': audioLevel }}
    >
      {/* Show initial circle animation when mic first turns on */}
      {showInitialCircle && (
        <div className="initial-circle" />
      )}

      {/* Show dots when mic is on and animation is complete */}
      {showDots && (
        <>
          <div
            className="dot"
            style={{ '--dot-scale': dot1Scale }}
          />
          <div
            className="dot"
            style={{ '--dot-scale': dot2Scale }}
          />
          <div
            className="dot"
            style={{ '--dot-scale': dot3Scale }}
          />
        </>
      )}

      {/* Show static circle when mic is off */}
      {!isActive && !showInitialCircle && !showDots && (
        <div className="static-circle" />
      )}
    </div>
  );
}

export default RealTimeAudioWave;
