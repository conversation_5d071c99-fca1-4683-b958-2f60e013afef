.audio-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 0.5rem;
  height: 24px; // Fixed height for consistent wave pattern
  position: relative;

  .dot {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    opacity: 0.3;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
    position: relative;

    // Beautiful gradient background with glow effect
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow:
      0 0 6px rgba(102, 126, 234, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);

    // Subtle inner glow
    &::before {
      content: '';
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      z-index: -1;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    // Different base colors for each dot to create unique identity
    &:nth-child(1) {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      animation-delay: 0s;

      &::before {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
    }

    &:nth-child(2) {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      animation-delay: 0.15s;

      &::before {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }
    }

    &:nth-child(3) {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      animation-delay: 0.3s;

      &::before {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
    }
  }

  &.active .dot {
    opacity: 0.7;
    transform: scale(1.1);

    &::before {
      opacity: 0.3;
    }
  }

  &.speaking .dot {
    opacity: 1;
    animation: wave-motion 0.6s ease-in-out infinite;

    &::before {
      opacity: 0.6;
    }

    // Enhanced glow and different animation delays for zigzag effect
    &:nth-child(1) {
      animation-delay: 0s;
      box-shadow:
        0 0 12px rgba(102, 126, 234, 0.6),
        0 0 24px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
    &:nth-child(2) {
      animation-delay: 0.2s;
      box-shadow:
        0 0 12px rgba(240, 147, 251, 0.6),
        0 0 24px rgba(245, 87, 108, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
    &:nth-child(3) {
      animation-delay: 0.4s;
      box-shadow:
        0 0 12px rgba(79, 172, 254, 0.6),
        0 0 24px rgba(0, 242, 254, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
  }
}

// Wave motion for when speaking - creates zigzag pattern
@keyframes wave-motion {
  0%, 100% {
    transform: translateY(0) scale(var(--dot-scale, 1));
  }
  25% {
    transform: translateY(-3px) scale(var(--dot-scale, 1));
  }
  50% {
    transform: translateY(0) scale(var(--dot-scale, 1));
  }
  75% {
    transform: translateY(3px) scale(var(--dot-scale, 1));
  }
}
