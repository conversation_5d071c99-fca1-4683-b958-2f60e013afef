.audio-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;

  .dot {
    width: 8px;
    height: 8px;
    background: #0a84ff;
    border-radius: 50%;
    opacity: 0.3;
    transition: all 0.2s ease;

    &:nth-child(2) { animation-delay: 0.1s; }
    &:nth-child(3) { animation-delay: 0.2s; }
  }

  &.active .dot {
    opacity: 0.6;
    animation: pulse 2s ease-in-out infinite;
  }

  &.speaking .dot {
    opacity: 1;
    transform: scale(calc(1 + var(--audio-level) * 0.5));
    box-shadow: 0 0 calc(var(--audio-level) * 10px) rgba(10, 132, 255, 0.6);
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.6; }
  50% { transform: scale(1.1); opacity: 0.8; }
}
