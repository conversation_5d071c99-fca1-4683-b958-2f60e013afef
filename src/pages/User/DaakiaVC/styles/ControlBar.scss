@import "./variables";

.control-bar {
  &-more-options{
    position: relative;
    height: 100%;
    .lk-button{
      height: 100%;
    }
    span{
      opacity: 0;
      visibility: hidden; // Hide it visually
      position: absolute;
      top: -1.8rem;
      width: max-content;
      left: 50%;
      background-color: #2b2b2b;
      color: white;
      padding: 0rem 0.5rem;
      border-radius: 5px;
      box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.75);
      z-index: 10;
      transform: translateX(-50%);
      transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out; // Add smooth effect
    }
    &:hover{
      span{
        opacity: 1;
        visibility: visible;
      }
    }
  }
  &-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1.5rem;

    .ant-badge {
      height: 100%;
      .lk-button{
        height: 100%;
      }
    }
    >span{
      height: 100%;
      .lk-button{
        height: 100%;
      }
    }

    @media screen and (max-width: 950px) {
      gap: 1rem;
    }
  }
}

.controlbar-timer {
  position: absolute;
  left: 20px;
  font-family: $font;
}

.controlbar-timer-recording-on {
  position: absolute;
  left: 50px;
}

.control-bar-button {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  width: auto;
  &:hover {
    background-color: #2d2d38;
  }
  &-icon{
    // height: 2.4rem !important;
  }
  svg {
    width: 25px;
    height: 25px;
  }
  &-chat{
    svg{
      width: 23px;
    }
  }
  &-raise-hand{
    svg{
      width: 23px;
    }
  }
  &-participants{
    svg{
      width: 23px;
      height: 23px;
    }
  }
  &-reactions{
    svg{
      width: 24px;
      // height: 23px;
    }
  }
}
.control-bar-button-raise-hand, .control-bar-button-participants, .control-bar-button-reactions{
  height: 100%;
  .lk-button{
    height: 100%;
  }
}

// ControlBar Icons
.chat-icon {
  svg {
    width: 24px !important;
  }
}
.raise-hand-icon{
  svg{
    width: 23px !important;
  }
}
.participants-icon{
  svg{
    width: 23px !important;
    height: 23px !important;
  }
}

.control-bar-icon {
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  &:hover svg {
    fill: #0a84ff; /* Change color on hover */
  }
}

.control-bar-button-icon {
  // height: calc(100% - 2px);
  padding: 6px 12px !important;
}

@media (max-width: 950px) {
  .controlbar-timer {
    display: none;
  }
  .controlbar-timer-recording-on {
    display: none !important;
  }
}

@media (max-width: 860px) {
  .control-bar-container {
    gap: 1.2rem;
  }
  .control-bar-button-icon {
    padding: 4px 10px !important;
  }
}

@media (max-width: 780px) {
  .control-bar-container {
    gap: 0.9rem;
  }
  .control-bar-container {
    .recording-icon {
      display: none;
    }
  }
}
@media (max-width: 450px) {
  #root {
    height: 100svh !important;
  }
}

// SettingsControlButton.js
@media screen and (max-width: 450px) {
  .threedots-popover-button {
    z-index: 10;
    .ant-popover-inner {
      width: 190px;
      .ant-popover-inner-content {
        width: 100%;
        .primary-font {
          width: 100% !important;
          .mvt-options {
            display: flex;
            flex-direction: row;
          }
          .mvt-option-text {
            font-size: 1rem;
            color: white;
          }
        }
      }
    }
  }
}
.control-bar-hover-label{
  padding: 0;
  .ant-popover-inner{
    border-radius: 5px;
  }
  .ant-popover-inner-content{
    padding: 0 5px;
    background-color: #2b2b2b;
    color: white;
    border-radius: 3px;
  }
}

// Audio Control Group - Better organized structure
.audio-control-group {


  // Left audio device selector
  &__device-left {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #1f1f1f;
    border-radius: 10px 0px 0px 10px;
  }

  // Center microphone toggle
  &__microphone-toggle {
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  // Right audio device selector
  &__device-right {
    .lk-button-group-menu {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}
